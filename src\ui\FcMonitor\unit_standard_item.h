#ifndef UNIT_STANDARD_ITEM_H
#define UNIT_STANDARD_ITEM_H

#include <QStandardItem>
#include <QStringList>

/**
 * @brief 支持单位切换的QStandardItem扩展类
 * 用于在TreeView中显示支持单位转换的信号项
 */
class UnitStandardItem : public QStandardItem
{
public:
    explicit UnitStandardItem(const QString& text = QString());
    explicit UnitStandardItem(const QIcon& icon, const QString& text);
    
    // 单位切换相关属性
    bool isHaveCombox() const { return m_isHaveCombox; }
    void setIsHaveCombox(bool hasCombox) { m_isHaveCombox = hasCombox; }
    
    const QStringList& unitList() const { return m_unitList; }
    void setUnitList(const QStringList& unitList) { m_unitList = unitList; }
    
    unsigned int defaultUnitID() const { return m_defaultUnitID; }
    void setDefaultUnitID(unsigned int unitID) { m_defaultUnitID = unitID; }
    
    const QString& defaultUnitName() const { return m_defaultUnitName; }
    void setDefaultUnitName(const QString& unitName) { m_defaultUnitName = unitName; }
    
    unsigned int currentUnitID() const { return m_currentUnitID; }
    void setCurrentUnitID(unsigned int unitID) { m_currentUnitID = unitID; }
    
    const QString& currentUnitName() const { return m_currentUnitName; }
    void setCurrentUnitName(const QString& unitName) { m_currentUnitName = unitName; }
    
    // 重写clone方法以支持正确的复制
    QStandardItem* clone() const override;

private:
    bool m_isHaveCombox;           // 是否支持单位切换
    QStringList m_unitList;        // 可用单位列表
    unsigned int m_defaultUnitID;  // 默认单位ID
    QString m_defaultUnitName;     // 默认单位名称
    unsigned int m_currentUnitID;  // 当前单位ID
    QString m_currentUnitName;     // 当前单位名称
};

#endif // UNIT_STANDARD_ITEM_H
