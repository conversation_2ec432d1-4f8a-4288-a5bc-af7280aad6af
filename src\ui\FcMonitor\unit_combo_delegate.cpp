#include "unit_combo_delegate.h"
#include "unit_standard_item.h"
#include "utils/unit_model.h"
#include <QComboBox>
#include <QPainter>
#include <QApplication>
#include <QDebug>
#include <QStandardItemModel>
#include <QMouseEvent>
#include <QEvent>
#include <QTimer>
#include <QMetaObject>
#include <QAbstractItemView>

UnitComboDelegate::UnitComboDelegate(QObject *parent)
    : QStyledItemDelegate(parent)
{
}

QWidget *UnitComboDelegate::createEditor(QWidget *parent, const QStyleOptionViewItem &option,
                                        const QModelIndex &index) const
{
    Q_UNUSED(option)
    qDebug()<<"createEditor";

    // 只在单位列（第3列，索引为2）且支持单位切换时创建下拉框
    if (!isUnitColumn(index)) {
        return QStyledItemDelegate::createEditor(parent, option, index);
    }

    QComboBox *comboBox = new QComboBox(parent);
    comboBox->setFrame(false);

    // 获取UnitStandardItem
    QStandardItemModel *model = qobject_cast<QStandardItemModel*>(const_cast<QAbstractItemModel*>(index.model()));
    if (model) {
        QStandardItem *item = model->itemFromIndex(index);
        UnitStandardItem *unitItem = dynamic_cast<UnitStandardItem*>(item);

        if (unitItem && unitItem->isHaveCombox()) {
            // 添加可用单位列表到下拉框
            comboBox->addItems(unitItem->unitList());

            //设置当前选中项
            QString currentUnitName = unitItem->currentUnitName();
            int currentIndex = comboBox->findText(currentUnitName);
            if (currentIndex >= 0) {
                comboBox->setCurrentIndex(currentIndex);
            }

            // 连接信号，当选择改变时自动提交数据并关闭编辑器
            connect(comboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
                    this, &UnitComboDelegate::commitAndCloseEditor);
        }
    }

    return comboBox;
}

void UnitComboDelegate::setEditorData(QWidget *editor, const QModelIndex &index) const
{
    qDebug()<<"setEditorData";
    if (!isUnitColumn(index)) {
        QStyledItemDelegate::setEditorData(editor, index);
        return;
    }

    QComboBox *comboBox = qobject_cast<QComboBox*>(editor);
    if (!comboBox) {
        return;
    }

    // 获取当前单位名称并设置到下拉框
    QStandardItemModel *model = qobject_cast<QStandardItemModel*>(const_cast<QAbstractItemModel*>(index.model()));
    if (model) {
        QStandardItem *item = model->itemFromIndex(index);
        UnitStandardItem *unitItem = dynamic_cast<UnitStandardItem*>(item);

        if (unitItem && unitItem->isHaveCombox()) {
            QString currentUnitName = unitItem->currentUnitName();
            int currentIndex = comboBox->findText(currentUnitName);
            if (currentIndex >= 0) {
                comboBox->setCurrentIndex(currentIndex);
            }

            // 延迟显示下拉列表，确保编辑器完全初始化
            QTimer::singleShot(100, comboBox, &QComboBox::showPopup);
        }
    }
}

void UnitComboDelegate::setModelData(QWidget *editor, QAbstractItemModel *model,
                                    const QModelIndex &index) const
{
     qDebug()<<"setModelData";
    if (!isUnitColumn(index)) {
        QStyledItemDelegate::setModelData(editor, model, index);
        return;
    }

    QComboBox *comboBox = qobject_cast<QComboBox*>(editor);
    if (!comboBox) {
        return;
    }

    QStandardItemModel *standardModel = qobject_cast<QStandardItemModel*>(model);
    if (standardModel) {
        QStandardItem *item = standardModel->itemFromIndex(index);
        UnitStandardItem *unitItem = dynamic_cast<UnitStandardItem*>(item);

        if (unitItem && unitItem->isHaveCombox()) {
            QString newUnitName = comboBox->currentText();
            QString oldUnitName = unitItem->currentUnitName();
            unsigned int oldUnitID = unitItem->currentUnitID();

            // 获取新单位的ID
            unsigned int newUnitID = UnitModel::Instance()->GetUnitIDFromName(newUnitName);

            // 更新UnitStandardItem的当前单位信息
            unitItem->setCurrentUnitName(newUnitName);
            unitItem->setCurrentUnitID(newUnitID);
            unitItem->setText(newUnitName);

            // 发射单位切换信号
            //emit const_cast<UnitComboDelegate*>(this)->unitChanged(index, oldUnitName, newUnitName, oldUnitID, newUnitID);

            qDebug() << "Unit changed from" << oldUnitName << "(" << oldUnitID << ") to"
                     << newUnitName << "(" << newUnitID << ")";
        }
    }
}

void UnitComboDelegate::updateEditorGeometry(QWidget *editor, const QStyleOptionViewItem &option,
                                            const QModelIndex &index) const
{
    Q_UNUSED(index)
    editor->setGeometry(option.rect);
}

void UnitComboDelegate::paint(QPainter *painter, const QStyleOptionViewItem &option,
                             const QModelIndex &index) const
{
    if (!isUnitColumn(index)) {
        QStyledItemDelegate::paint(painter, option, index);
        return;
    }

    // 检查是否支持单位切换
    QStandardItemModel *model = qobject_cast<QStandardItemModel*>(const_cast<QAbstractItemModel*>(index.model()));
    if (model) {
        QStandardItem *item = model->itemFromIndex(index);
        UnitStandardItem *unitItem = dynamic_cast<UnitStandardItem*>(item);

        if (unitItem && unitItem->isHaveCombox()) {
            // 绘制带下拉箭头的样式，表示可以点击切换
            QStyleOptionComboBox comboBoxOption;
            comboBoxOption.rect = option.rect;
            comboBoxOption.state = option.state | QStyle::State_Enabled;
            comboBoxOption.currentText = unitItem->currentUnitName();
            comboBoxOption.frame = true;

            // 如果鼠标悬停，添加悬停状态
            if (option.state & QStyle::State_MouseOver) {
                comboBoxOption.state |= QStyle::State_MouseOver;
            }

            // 绘制ComboBox背景和边框
            QApplication::style()->drawComplexControl(QStyle::CC_ComboBox, &comboBoxOption, painter);

            // 绘制文本标签
            QApplication::style()->drawControl(QStyle::CE_ComboBoxLabel, &comboBoxOption, painter);
            return;
        }
    }

    // 不支持单位切换的项目使用默认绘制
    QStyledItemDelegate::paint(painter, option, index);
}

void UnitComboDelegate::commitAndCloseEditor()
{
    QComboBox *editor = qobject_cast<QComboBox*>(sender());
    if (editor) {
        emit commitData(editor);
        emit closeEditor(editor);
    }
}

bool UnitComboDelegate::isUnitColumn(const QModelIndex &index) const
{
    // 单位列是第3列（索引为2）
    return index.column() == 2;
}

bool UnitComboDelegate::editorEvent(QEvent *event, QAbstractItemModel *model,
                                   const QStyleOptionViewItem &option, const QModelIndex &index)
{
    qDebug()<<"editorEvent";
    // 只处理单位列的鼠标事件
    if (!isUnitColumn(index)) {
        return QStyledItemDelegate::editorEvent(event, model, option, index);
    }

    // 检查是否支持单位切换
    QStandardItemModel *standardModel = qobject_cast<QStandardItemModel*>(model);
    if (!standardModel) {
        return QStyledItemDelegate::editorEvent(event, model, option, index);
    }

    QStandardItem *item = standardModel->itemFromIndex(index);
    UnitStandardItem *unitItem = dynamic_cast<UnitStandardItem*>(item);

    if (!unitItem || !unitItem->isHaveCombox()) {
        return QStyledItemDelegate::editorEvent(event, model, option, index);
    }

    // 处理鼠标按下事件
    if (event->type() == QEvent::MouseButtonPress) {
        QMouseEvent *mouseEvent = static_cast<QMouseEvent*>(event);
        if (mouseEvent->button() == Qt::LeftButton) {
            // 直接触发编辑
            QAbstractItemView *view = qobject_cast<QAbstractItemView*>(const_cast<QWidget*>(option.widget));
            if (view) {
                view->edit(index);
                return true;
            }
        }
    }

    qDebug()<<"editorEvent2";
    return QStyledItemDelegate::editorEvent(event, model, option, index);
}

ComboxDelegate::ComboxDelegate(QObject* parent):QStyledItemDelegate(parent)
{
}
/*!
*@brief			析构函数
*
*
*/
ComboxDelegate::~ComboxDelegate()
{
}
/*!
*@brief			创建widget
*
*@param QWidget *parent							   父widget指针
*@param const QStyleOptionViewItem &option         option
*@param const QModelIndex &index				   索引
*
*@param return  创建的widget
*/
QWidget* ComboxDelegate::createEditor(QWidget *parent, const QStyleOptionViewItem &option, const QModelIndex &index) const
{
    //获取所在的model
    const QStandardItemModel* pQStandardItemModel = dynamic_cast<const QStandardItemModel*>(index.model());
    if (NULL == pQStandardItemModel)
    {
        return NULL;
    }
    //获取所在的item
    UnitStandardItem* pm_StandardItem = dynamic_cast<UnitStandardItem*>(pQStandardItemModel->itemFromIndex(index));
    if (NULL == pm_StandardItem)
    {
        return NULL;
    }
    //如果不需要创建combox
    if (!pm_StandardItem->isHaveCombox())
    {
        return NULL;
    }
    //先设置显示为空
    pm_StandardItem->setText("");
    //创建combox
    QComboBox* pQComboBox = new QComboBox(parent);
    //设置样式表
    pQComboBox->setStyleSheet(QString::fromUtf8("QComboBox{border: none;background:transparent;}"));
    return pQComboBox;
}
/*!
*@brief			设置创建的wiget的数据
*
*@param QWidget *editor							   创建的widget
*@param const QModelIndex &index				   索引
*
*@param return
*/
void ComboxDelegate::setEditorData(QWidget *editor, const QModelIndex &index) const
{
    //获取创建的combox
    QComboBox* pQComboBox = dynamic_cast<QComboBox*>(editor);
    if (NULL == pQComboBox)
    {
        return;
    }
    //获取所在的model
    const QStandardItemModel* pQStandardItemModel = dynamic_cast<const QStandardItemModel*>(index.model());
    if (NULL == pQStandardItemModel)
    {
        return;
    }
    //获取所在的item
    UnitStandardItem* pm_StandardItem = dynamic_cast<UnitStandardItem*>(pQStandardItemModel->itemFromIndex(index));
    if (NULL == pm_StandardItem)
    {
        return;
    }
    //记录combox的默认索引位置
    int CurrentIndex = 0;
    int iindex = 0;
    //当前单位名称
    QString currentUnitName;
    //如果为0说明用户还未选择单位
    if (0==pm_StandardItem->currentUnitID())
    {
        currentUnitName = pm_StandardItem->defaultUnitID();
    }
    else
    {
        currentUnitName = pm_StandardItem->currentUnitID();
    }
    //遍历单位列表往combox里面添加单位名
    foreach(const QString& trUnitName ,pm_StandardItem->unitList())
    {
        //如果为当前单位则记录索引
        if (currentUnitName == trUnitName)
        {
            CurrentIndex = iindex;
        }
        pQComboBox->addItem(trUnitName);
        iindex++;
    }
    //设置当前索引为当前单位所在的位置
    pQComboBox->setCurrentIndex(CurrentIndex);
}
/*!
*@brief			设置树型结构控件的数据
*
*@param QWidget *editor							   创建的widget
*@param QAbstractItemModel *model				   所在的model
*@param const QModelIndex &index				   索引
*
*@param return
*/
void ComboxDelegate::setModelData(QWidget *editor, QAbstractItemModel *model, const QModelIndex &index) const
{
    //获取创建的combox
    QComboBox* pQComboBox = dynamic_cast<QComboBox*>(editor);
    if (NULL == pQComboBox)
    {
        return;
    }
    //获取所在的model
    QStandardItemModel* pQStandardItemModel = dynamic_cast<QStandardItemModel*>(model);
    if (NULL == pQStandardItemModel)
    {
        return;
    }
    //获取所在的item
    UnitStandardItem* pm_StandardItem = dynamic_cast<UnitStandardItem*>(pQStandardItemModel->itemFromIndex(index));
    if (NULL == pm_StandardItem)
    {
        return;
    }
    //获取当前的单位名
    QString CurrentUnit = pQComboBox->currentText();
    //保存单位名
    pm_StandardItem->setCurrentUnitName(CurrentUnit);
    //保存ID
    pm_StandardItem->setCurrentUnitID(UnitModel::Instance()->GetUnitIDFromName(CurrentUnit));
    pm_StandardItem->setText(CurrentUnit);

    //获取信号值的item
    QStandardItem* pSigItem = pm_StandardItem->parent()->child(index.row(),1);
    //获取ID
    unsigned int CurrentUnitID = pm_StandardItem->currentUnitID();
    unsigned int DefaultUnitID = pm_StandardItem->defaultUnitID();
    //进行单位转换
//    double SigVal = UnitModel::unit_change(DefaultUnitID,CurrentUnitID,pm_StandardItem->SigValue);
//    //对时分秒进行特殊转换
//    if(CurrentUnit == QString::fromLocal8Bit("时分秒"))
//    {
//        unsigned int UnintSigVal = SigVal;
//        unsigned int UnintHour = UnintSigVal/3600;
//        unsigned int UnintMin = UnintSigVal%3600/60;
//        unsigned int UnintSec = UnintSigVal%3600%60;
//        QString strSigval;
//        strSigval.sprintf("%02d %02d %02d",UnintHour,UnintMin,UnintSec);
//        pSigItem->setText(strSigval);
//        pSigItem->setToolTip(strSigval);

//    }
//    else
//    {
//        QString strSigval = QString::number(SigVal,'f');
//        pSigItem->setText(strSigval);
//        pSigItem->setToolTip(strSigval);

//    }
}
/*!
*@brief			更新创建的widget的大小
*
*@param QWidget *editor							   创建的widget
*@param QStyleOptionViewItem &option			   option
*@param const QModelIndex &index				   索引
*
*@param return
*/
void ComboxDelegate::updateEditorGeometry(QWidget *editor, const QStyleOptionViewItem &option, const QModelIndex &index) const
{
    if (NULL != editor)
    {
        editor->setGeometry(option.rect);
    }
}
