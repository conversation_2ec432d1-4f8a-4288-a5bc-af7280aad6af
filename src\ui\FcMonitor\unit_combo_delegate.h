#ifndef UNIT_COMBO_DELEGATE_H
#define UNIT_COMBO_DELEGATE_H

#include <QStyledItemDelegate>
#include <QComboBox>
#include <QModelIndex>

/**
 * @brief 单位下拉框委托类
 * 用于在TreeView的单位列中显示下拉框，支持单位切换
 */
class UnitComboDelegate : public QStyledItemDelegate
{
    Q_OBJECT

public:
    explicit UnitComboDelegate(QObject *parent = nullptr);

    // 创建编辑器（下拉框）
    QWidget *createEditor(QWidget *parent, const QStyleOptionViewItem &option,
                         const QModelIndex &index) const override;

    // 设置编辑器数据
    void setEditorData(QWidget *editor, const QModelIndex &index) const override;

    // 设置模型数据
    void setModelData(QWidget *editor, QAbstractItemModel *model,
                     const QModelIndex &index) const override;

    // 更新编辑器几何形状
    void updateEditorGeometry(QWidget *editor, const QStyleOptionViewItem &option,
                             const QModelIndex &index) const override;

    // 绘制项目（显示当前选中的单位）
    void paint(QPainter *painter, const QStyleOptionViewItem &option,
               const QModelIndex &index) const override;

    // 添加这个方法来处理鼠标事件
     bool editorEvent(QEvent *event, QAbstractItemModel *model,
                     const QStyleOptionViewItem &option,
                     const QModelIndex &index) override;
signals:
    // 单位切换信号
    void unitChanged(const QModelIndex &index, const QString &oldUnit, const QString &newUnit,
                    unsigned int oldUnitID, unsigned int newUnitID);

private slots:
    // 下拉框选择改变时的槽函数
    void commitAndCloseEditor();

private:
    // 检查是否是单位列且支持单位切换
    bool isUnitColumn(const QModelIndex &index) const;
};

class ComboxDelegate:public QStyledItemDelegate
{
    Q_OBJECT
public:
    //构造函数
    explicit ComboxDelegate(QObject* parent=0);
    //析构函数
    ~ComboxDelegate();
    //创建widget
    QWidget* createEditor(QWidget *parent, const QStyleOptionViewItem &option, const QModelIndex &index) const;
    //设置创建的wiget的数据
    void setEditorData(QWidget *editor, const QModelIndex &index) const;
    //设置树型结构控件的数据
    void setModelData(QWidget *editor, QAbstractItemModel *model, const QModelIndex &index) const;
    //更新创建的widget的大小
    void updateEditorGeometry(QWidget *editor, const QStyleOptionViewItem &option, const QModelIndex &index) const;
};

#endif // UNIT_COMBO_DELEGATE_H
